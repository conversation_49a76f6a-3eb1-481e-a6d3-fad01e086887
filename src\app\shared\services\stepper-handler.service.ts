import { Injectable } from '@angular/core';
import { ToastService } from './toast.service';
import { ErrorHandlerService } from './error-handler.service';
import {
  StepperConfig,
  StepHandlerConfig,
  StepperState
} from '../components/generic-stepper/stepper.interfaces';
import {
  preTransformData,
  postTransformData,
  resolveServiceMethod,
  generateStepperSuccessMessage,
  executeServiceCall
} from '../utils/handler.utils';

@Injectable({
  providedIn: 'root'
})
export class StepperHandlerService {

  constructor(
    private toastService: ToastService,
    private errorHandlerService: ErrorHandlerService
  ) { }

  // PUBLIC ACTION METHODS - for use in config onClick handlers

  goBack(stepperState: StepperState, activateCallback?: (step: number) => void): void {
    if (stepperState.currentStep > 1) {
      stepperState.currentStep--;

      // When going back to a step that has data, set it to 'view' mode
      if (stepperState.stepData[stepperState.currentStep]) {
        stepperState.stepModes[stepperState.currentStep] = 'view';
      }

      if (activateCallback) {
        activateCallback(stepperState.currentStep);
      }
    }
  }

  enableEdit(stepperState: StepperState): void {
    // Set current step to 'edit' mode
    stepperState.stepModes[stepperState.currentStep] = 'edit';
  }

  skipStep(_stepperState: StepperState, stepperConfig: StepperConfig, hideDialogCallback?: () => void): void {
    if (hideDialogCallback) {
      hideDialogCallback();
    } else if (stepperConfig.onComplete) {
      stepperConfig.onComplete();
    }
  }

  cancel(stepperConfig: StepperConfig): void {
    if (stepperConfig.onCancel) stepperConfig.onCancel();
  }

  async goNext(
    stepperState: StepperState,
    stepperConfig: StepperConfig,
    activateCallback?: (step: number) => void,
    currentStepConfig?: StepHandlerConfig
  ): Promise<void> {
    const currentStepMode = stepperState.stepModes[stepperState.currentStep];

    // If in 'view' mode, just navigate without API call
    if (currentStepMode === 'view') {
      this.navigateToNextStep(stepperState, stepperConfig, activateCallback);
      return Promise.resolve();
    }

    // For 'create' and 'edit' modes, make API call
    if (!currentStepConfig) return Promise.resolve();

    stepperState.isLoading = true;

    // Determine if this is an update or create operation
    const isUpdate = currentStepMode === 'edit';

    // Get current step's entityLabel
    const currentStep = stepperConfig.steps.find(s => s.value === stepperState.currentStep);
    const entityLabel = currentStep?.entityLabel;

    // Execute the step action directly
    try {
      // Validate & extract form
      const form = stepperState.forms[stepperState.currentStep];
      if (!form || form.invalid) {
        console.error('Step execution failed: Form is invalid');
        return;
      }
      const formValue = form.getRawValue();

      // Transform data using unified utility function (includes isUpdate parameter)
      const transformedData = preTransformData(currentStepConfig, formValue, isUpdate, stepperState);

      // Resolve service method using utility function
      const methodResult = resolveServiceMethod(currentStepConfig, isUpdate);
      if (!methodResult.success) {
        console.error('Step execution failed:', methodResult.error);
        return;
      }

      // Execute service method call & success message
      const serviceCall = methodResult.serviceCall.call(currentStepConfig.service, transformedData);
      const successMessage = generateStepperSuccessMessage(entityLabel, isUpdate);

      const result = await executeServiceCall(serviceCall, successMessage, this.errorHandlerService, this.toastService);

      // Post-transform/store the result data & navigate after API
      if (result.success) {
        if (form && currentStepConfig) {
          const transformedStepData = postTransformData(currentStepConfig, result.data, formValue, stepperState);
          stepperState.stepData[stepperState.currentStep] = transformedStepData;
        } else {
          stepperState.stepData[stepperState.currentStep] = result.data;
        }

        // After successful API call, set current step to 'view' mode
        stepperState.stepModes[stepperState.currentStep] = 'view';
        console.log(`Set step ${stepperState.currentStep} to view mode`);

        // Navigate to next step
        this.navigateToNextStep(stepperState, stepperConfig, activateCallback);
      } else {
        console.error('Step execution failed:', result.error);
      }
    } catch (error) {
      console.error('Step execution failed:', error);
    } finally {
      stepperState.isLoading = false;
    }
  }

  private navigateToNextStep(
    stepperState: StepperState,
    stepperConfig: StepperConfig,
    activateCallback?: (step: number) => void
  ): void {
    // Check if this is the last step & complete the stepper if onComplete is provided
    if (stepperState.currentStep >= stepperConfig.steps.length) {
      if (stepperConfig.onComplete) stepperConfig.onComplete();
    } else {
      // Advance to next step
      stepperState.currentStep++;
      if (activateCallback) {
        activateCallback(stepperState.currentStep);
      }
    }
  }

}
